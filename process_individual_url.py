import requests
import json
import pandas as pd
from datetime import datetime, timezone
import re

# File path
URLS_JSON_FILE = "urls.json"

def load_urls_json():
    """Load the URLs JSON file."""
    try:
        with open(URLS_JSON_FILE, "r", encoding="utf8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading URLs JSON file: {e}")
        return {"urls": []}

def save_urls_json(data):
    """Save the URLs JSON file."""
    try:
        with open(URLS_JSON_FILE, "w", encoding="utf8") as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving URLs JSON file: {e}")
        return False

def load_street_types(file_path="prefix_template.json"):
    """Load street type variations from a JSON file."""
    with open(file_path, "r") as file:
        return json.load(file)

def replace_street_types(address):
    """Replace street type variations with standard forms."""
    if not address:
        return address

    street_types = load_street_types()
    replacement_map = {variation.lower(): standard for standard, variations in street_types.items() for variation in variations}

    def replacer(match):
        word = match.group()
        return replacement_map.get(word.lower(), word)

    # Use regular expression to match whole words only
    pattern = r'\b(' + '|'.join(re.escape(key) for key in replacement_map.keys()) + r')\b'
    return re.sub(pattern, replacer, address, flags=re.IGNORECASE)

def load_variations(file_path="building_variations.txt"):
    """Load variations from a text file to match building identifiers."""
    try:
        with open(file_path, "r") as file:
            return [line.strip() for line in file.readlines() if line.strip()]
    except FileNotFoundError:
        print(f"Warning: {file_path} not found. Using default variations.")
        return ["unit", "apt", "apartment", "suite", "#"]

def get_building_name(address, file_path="building_variations.txt"):
    """Extract the building name from an address before keywords."""
    if not address:
        return address

    variations = load_variations(file_path)
    pattern = r'|'.join([fr'\b{re.escape(var.lower())}\b' for var in variations])

    match = re.split(pattern, address.lower(), maxsplit=1, flags=re.IGNORECASE)
    return match[0].strip() if match else address.strip()

def get_building_number(address, file_path="building_variations.txt"):
    """Extract the building number from an address after keywords."""
    if not address:
        return None

    variations = load_variations(file_path)
    pattern = r'(?i)(?:#|' + '|'.join(re.escape(var) for var in variations) + r')\s*([\w\d-]+)'

    try:
        match = re.search(pattern, address, re.IGNORECASE)
        return match.group(1) if match else None
    except:
        return None

def format_price_history(price_history):
    """Format price history into a readable string."""
    if not price_history:
        return ""

    formatted_history = []
    for record in price_history:
        try:
            # Convert timestamp to datetime using timezone-aware method
            date_str = datetime.fromtimestamp(record['time'] / 1000, tz=timezone.utc).strftime('%Y-%m-%d')

            # Format price as currency
            formatted_price = "${:,.0f}".format(record.get("price", 0))

            # Create the formatted output string
            output_str = f"{date_str}: {formatted_price} ({record['event']})"
            formatted_history.append(output_str)
        except Exception:
            # Silently ignore errors in price history formatting
            pass

    # Return the formatted history as a single string
    return ", \n".join(formatted_history)

def convert_schools_to_format(school_list):
    """Convert school list to a formatted string."""
    if not school_list:
        return ""

    formatted_schools = []
    for school in school_list:
        # Get school name
        name = school.get('name', 'Unknown')

        # Get rating (might be in different formats)
        rating = school.get('rating', 'N/A')

        # Format the school information
        formatted_schools.append(f"Rating: {rating} Name: {name}")

    return ', '.join(formatted_schools)

def extract_appliances(appliances_list):
    """Extract appliances from the property details."""
    if not appliances_list:
        return ""

    for item in appliances_list:
        if item['title'] == 'Appliances':
            appliances = item['values'][0]  # Get the first value in the list
            # Split by comma and remove unwanted words/phrases
            appliances = appliances.replace('Appliances included: ', '').replace('Hot Water: Gas Stand Alone', 'Hot Water: On Demand')
            appliance_list = appliances.split(',')
            return ', '.join([appliance.strip() for appliance in appliance_list])
    return ''

def extract_heating(appliances_list):
    """Extract heating information from the property details."""
    if not appliances_list:
        return ""

    for item in appliances_list:
        if item['title'] == 'Heating':
            appliances = item['values'][0]  # Get the first value in the list
            # Split by comma and remove unwanted words/phrases
            appliances = appliances.replace('Appliances included: ', '').replace('Hot Water: Gas Stand Alone', 'Hot Water: On Demand')
            appliance_list = appliances.split(',')
            return ', '.join([appliance.strip() for appliance in appliance_list])
    return ''

def extract_cooling(appliances_list):
    """Extract cooling information from the property details."""
    if not appliances_list:
        return ""

    for item in appliances_list:
        if item['title'] == 'Cooling':
            try:
                appliances = item['values'][0]  # Get the first value in the list
                # Split by comma and remove unwanted words/phrases
                appliances = appliances.replace('Appliances included: ', '').replace('Hot Water: Gas Stand Alone', 'Hot Water: On Demand')
                appliance_list = appliances.split(',')
                return ', '.join([appliance.strip() for appliance in appliance_list])
            except:
                pass
    return ''

def extract_parking_details(property_list):
    """Extract parking details from the property details."""
    if not property_list:
        return ""

    for item in property_list:
        if item.get('title') == 'Parking':
            parking_features = []
            for value in item['values']:
                if "Parking features:" in value:
                    # Extract features after the colon
                    features = value.split("Parking features:")[-1].strip()
                    parking_features.extend([feature.strip() for feature in features.split(",")])
                elif "Has garage:" in value and "Yes" in value:
                    parking_features.append("Garage")
                elif "Covered spaces:" in value and int(value.split(":")[-1].strip()) > 0:
                    parking_features.append("Covered")
                elif "Has uncovered spaces:" in value and "Yes" in value:
                    parking_features.append("Uncovered")
            # Return unique and formatted features
            return ', '.join(sorted(set(parking_features)))
    return ''

def format_open_house_details(details_list):
    """Format open house details into a readable string."""
    if not details_list or not isinstance(details_list, list):
        return ""

    formatted_details = []
    for detail in details_list:
        try:
            # Extract fields with default fallbacks
            day = detail.get('open_house_day', '')
            date_iso = detail.get('open_house_date', '')
            start_epoch = detail.get('open_house_start_time', None)
            end_epoch = detail.get('open_house_end_time', None)

            # Convert ISO date to readable format
            date = datetime.fromisoformat(date_iso.replace('Z', '')).strftime('%Y-%m-%d') if date_iso else ""

            # Convert epoch to readable time
            start_time = datetime.fromtimestamp(start_epoch / 1000).strftime('%I:%M %p') if start_epoch else ""
            end_time = datetime.fromtimestamp(end_epoch / 1000).strftime('%I:%M %p') if end_epoch else ""

            # Format the output string
            formatted_details.append(f"{day}, {date}, {start_time} - {end_time}")
        except Exception as e:
            print(f"Error formatting details: {e}")
            continue

    return '; '.join(formatted_details)

def get_first_source(price_history):
    """Get the first source from price history."""
    if not price_history or not isinstance(price_history, list):
        return None  # Return None for empty or invalid data
    return price_history[0].get('source', None)  # Extract "source" from the first dictionary

def extract_max_width_links(json_column):
    """Extract maximum width image links from JSON data."""
    max_links = []
    for photos_list in json_column:
        try:
            # Parse JSON if it's a string
            if isinstance(photos_list, str):
                photos_list = json.loads(photos_list)

            # Handle different photo formats
            image_links = []

            # For individual property format (list of photo objects)
            if isinstance(photos_list, list):
                for photo in photos_list:
                    # Direct URL format (most common in individual property format)
                    if isinstance(photo, str):
                        image_links.append(photo)
                    # Object format with URL
                    elif isinstance(photo, dict):
                        # Check for direct URL
                        if photo.get("url"):
                            image_links.append(photo.get("url"))
                        # Check for mixed_sources format
                        elif photo.get("mixed_sources", {}).get("jpeg", []):
                            jpeg_urls = photo.get("mixed_sources", {}).get("jpeg", [])
                            if jpeg_urls:
                                image_links.append(jpeg_urls[-1].get("url", None))
                        # Check for mixedSources format (camelCase)
                        elif photo.get("mixedSources", {}).get("jpeg", []):
                            jpeg_urls = photo.get("mixedSources", {}).get("jpeg", [])
                            if jpeg_urls:
                                image_links.append(jpeg_urls[-1].get("url", None))

            # For search results format (dictionary with mixedSources)
            elif isinstance(photos_list, dict):
                # Check for mixedSources format
                if photos_list.get("mixedSources", {}).get("jpeg", []):
                    jpeg_urls = photos_list.get("mixedSources", {}).get("jpeg", [])
                    if jpeg_urls:
                        image_links.append(jpeg_urls[-1].get("url", None))
                # Check for mixed_sources format (snake_case)
                elif photos_list.get("mixed_sources", {}).get("jpeg", []):
                    jpeg_urls = photos_list.get("mixed_sources", {}).get("jpeg", [])
                    if jpeg_urls:
                        image_links.append(jpeg_urls[-1].get("url", None))
                # Fallback to direct URL if available
                elif photos_list.get("url"):
                    image_links.append(photos_list.get("url"))

            # Print for debugging
            if image_links:
                print(f"Found {len(image_links)} images")
            else:
                print("No images found in this format, raw data:", str(photos_list)[:100])

            max_links.append(image_links)
        except Exception as e:
            print(f"Error extracting image links: {e}")
            max_links.append([])  # Handle errors gracefully
    return max_links

def extract_home_type(construction_json):
    """Extract home type from construction JSON."""
    if not construction_json:
        return None

    for item in construction_json:
        if item['title'] == 'Type & style':
            for value in item['values']:
                if 'Home type:' in value:
                    return value.split(":")[1].strip()
    return None

def upload_to_glideapps(cleaned_data):
    """Upload data to Glide Apps."""
    # Your API token and table ID
    api_token = "d394a9c6-e6fc-441b-aecb-602b6b53a0be"
    table_id = "4708265f-b64c-4388-bc64-2d93aebda2bf"  # Replace with your actual table ID

    # API endpoint
    url = f"https://api.glideapps.com/tables/{table_id}/rows"

    # Modify column names: Capitalize the first letter
    cleaned_data.columns = [col for col in cleaned_data.columns]
    print(cleaned_data.columns)

    # Prepare the payload from the updated cleaned_data DataFrame
    payload = cleaned_data.to_dict(orient="records")

    # Set headers for the request
    headers = {
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json"
    }

    # Send the POST request
    response = requests.post(url, json=payload, headers=headers)

    # Print the response from the API
    print("Status Code:", response.status_code)
    print("Response Body:", response.text)

    return response.status_code == 201

def process_data(response, snapshot_id):
    """Process data from the API response."""
    current_datetime = datetime.now()

    # Format the date and time in the desired format
    # Using ISO format which is more widely accepted
    fetchDate = current_datetime.strftime("%Y-%m-%d")

    # Print the first few keys to understand the data structure
    response_data = response.json()
    if isinstance(response_data, list) and len(response_data) > 0:
        print("Data keys:", list(response_data[0].keys())[:10], "...")

    # Create DataFrame from response
    df = pd.DataFrame(response_data)

    # List of columns to keep
    columns_to_keep = [
        "ZPID", "url", "fetchDate", "streetAddress", "Sorted Address", "Building Number", "city", "state", "zipcode",
        "price", "bedrooms", "bathrooms", "livingArea", "yearBuilt",
        "propertyType", "status", "daysOnZillow", "views", "saved",
        "priceHistory", "description", "schools", "propertyTaxRate",
        "annualHomeownersInsurance", "hoaFee", "appliances", "heating",
        "cooling", "parkingFeatures", "openHouseSchedule", "mlsId",
        "mlsName", "org_agentName", "org_agentPhoneNumber", "brokerName", "Brokerage Phone",
        "Agent 1 Name", "Agent 1 Phone", "Agent 2 Name", "Agent 2 Phone", "propertyJSON",
    ]

    for i in range(39):
        columns_to_keep.append(f"image {i+1}")

    # Print column names for debugging
    print("Available columns:", df.columns.tolist())

    # Add required columns and process data with safety checks for individual property format
    # Basic fields
    df['ZPID'] = df['zpid'] if 'zpid' in df.columns else None
    df['fetchDate'] = fetchDate
    df['url'] = df['url'] if 'url' in df.columns else None

    # Address fields
    if 'street_address' in df.columns:
        df['streetAddress'] = df['street_address']
    elif 'streetAddress' in df.columns:
        df['streetAddress'] = df['streetAddress']
    else:
        df['streetAddress'] = None

    df['city'] = df['city'] if 'city' in df.columns else None
    df['state'] = df['state'] if 'state' in df.columns else None
    df['zipcode'] = df['zipcode'] if 'zipcode' in df.columns else None

    # Property details
    df['price'] = df['price'] if 'price' in df.columns else None
    df['bedrooms'] = df['bedrooms'] if 'bedrooms' in df.columns else None
    df['bathrooms'] = df['bathrooms'] if 'bathrooms' in df.columns else None

    # Living area (square footage)
    if 'living_area' in df.columns:
        df['livingArea'] = df['living_area']
    elif 'livingArea' in df.columns:
        df['livingArea'] = df['livingArea']
    else:
        df['livingArea'] = None

    # Year built
    if 'year_built' in df.columns:
        df['yearBuilt'] = df['year_built']
    elif 'yearBuilt' in df.columns:
        df['yearBuilt'] = df['yearBuilt']
    else:
        df['yearBuilt'] = None

    # Property type - using home_type field for individual properties
    if 'home_type' in df.columns:
        df["propertyType"] = df['home_type']
    elif 'property_type' in df.columns:
        df["propertyType"] = df['property_type']
    elif 'construction' in df.columns:
        df["propertyType"] = df['construction'].apply(extract_home_type)
    else:
        df["propertyType"] = None

    # Status - using home_status field for individual properties
    if 'home_status' in df.columns:
        df["status"] = df['home_status']
    elif 'status' in df.columns:
        df["status"] = df['status']
    elif 'homeStatus' in df.columns:
        df["status"] = df['homeStatus']
    else:
        df["status"] = None

    # Views and saves
    if 'page_view_count' in df.columns:
        df['views'] = df['page_view_count']
    elif 'views' in df.columns:
        df['views'] = df['views']
    elif 'overview' in df.columns:
        df['views'] = df['overview'].apply(lambda x: x.get('number_of_views', None) if isinstance(x, dict) else None)
    else:
        df['views'] = None

    if 'favorite_count' in df.columns:
        df['saved'] = df['favorite_count']
    elif 'saved' in df.columns:
        df['saved'] = df['saved']
    elif 'overview' in df.columns:
        df['saved'] = df['overview'].apply(lambda x: x.get('number_of_saves', None) if isinstance(x, dict) else None)
    else:
        df['saved'] = None

    # Days on Zillow
    if 'days_on_zillow' in df.columns:
        df['daysOnZillow'] = df['days_on_zillow']
    elif 'daysOnZillow' in df.columns:
        df['daysOnZillow'] = df['daysOnZillow']
    elif 'time_on_zillow' in df.columns:
        df['daysOnZillow'] = df['time_on_zillow']
    else:
        df['daysOnZillow'] = None

    # Description
    if 'description' in df.columns:
        df['description'] = df['description']
    else:
        df['description'] = None

    # Schools
    if 'schools' in df.columns:
        df['schools'] = df['schools'].apply(convert_schools_to_format)
    else:
        df['schools'] = None

    # Property tax rate
    if 'property_tax_rate' in df.columns:
        df['propertyTaxRate'] = df['property_tax_rate']
    elif 'propertyTaxRate' in df.columns:
        df['propertyTaxRate'] = df['propertyTaxRate']
    else:
        df['propertyTaxRate'] = None

    # Annual homeowners insurance
    if 'annual_homeowners_insurance' in df.columns:
        df['annualHomeownersInsurance'] = df['annual_homeowners_insurance']
    elif 'annualHomeownersInsurance' in df.columns:
        df['annualHomeownersInsurance'] = df['annualHomeownersInsurance']
    else:
        df['annualHomeownersInsurance'] = None

    # HOA fee
    if 'hoa_fee' in df.columns:
        df['hoaFee'] = df['hoa_fee']
    elif 'hoaFee' in df.columns:
        df['hoaFee'] = df['hoaFee']
    elif 'hoa_details' in df.columns:
        df['hoaFee'] = df['hoa_details'].apply(lambda x: x.get('hoa_fee_value', None) if isinstance(x, dict) else None)
    else:
        df['hoaFee'] = None

    # Interior details
    if 'interior_full' in df.columns:
        df['appliances'] = df['interior_full'].apply(extract_appliances)
        df['heating'] = df['interior_full'].apply(extract_heating)
        df['cooling'] = df['interior_full'].apply(extract_cooling)
    else:
        df['appliances'] = None
        df['heating'] = None
        df['cooling'] = None

    # Parking features
    if 'property' in df.columns:
        try:
            df['parkingFeatures'] = df['property'].apply(extract_parking_details)
        except:
            df['parkingFeatures'] = ""
    else:
        df['parkingFeatures'] = ""

    # Open house schedule
    if 'open_house_details' in df.columns:
        df['openHouseSchedule'] = df['open_house_details'].apply(format_open_house_details)
    else:
        df['openHouseSchedule'] = ""

    # MLS ID
    if 'mls_id' in df.columns:
        df['mlsId'] = df['mls_id']
    elif 'mlsid' in df.columns:
        df['mlsId'] = df['mlsid']
    elif 'mlsId' in df.columns:
        df['mlsId'] = df['mlsId']
    else:
        df['mlsId'] = ""

    # Broker name
    if 'brokerage_name' in df.columns:
        df['brokerName'] = df['brokerage_name']
    elif 'brokerageName' in df.columns:
        df['brokerName'] = df['brokerageName']
    else:
        df['brokerName'] = ""

    # Price history
    if 'price_history' in df.columns:
        try:
            print("Price history sample:", df['price_history'].iloc[0] if not df['price_history'].empty else "No price history")
            df['priceHistory'] = df['price_history'].apply(format_price_history)
            df['mlsName'] = df['price_history'].apply(get_first_source)
        except Exception as e:
            print(f"Error formatting price history: {e}")
            df['priceHistory'] = ""
            df['mlsName'] = ""
    elif 'priceHistory' in df.columns:
        try:
            print("Price history sample:", df['priceHistory'].iloc[0] if not df['priceHistory'].empty else "No price history")
            df['priceHistory'] = df['priceHistory'].apply(format_price_history)
            df['mlsName'] = df['priceHistory'].apply(get_first_source)
        except Exception as e:
            print(f"Error formatting price history: {e}")
            df['priceHistory'] = ""
            df['mlsName'] = ""
    else:
        df['priceHistory'] = ""
        df['mlsName'] = ""

    # Agent information
    if 'listed_by' in df.columns:
        print("Listed by sample:", df['listed_by'].iloc[0] if not df['listed_by'].empty else "No listing agent")
        # For individual property format
        df['org_agentName'] = df['listed_by'].apply(lambda x: x.get('name', "") if isinstance(x, dict) else "")
        df['org_agentPhoneNumber'] = df['listed_by'].apply(lambda x: x.get('phone', "") if isinstance(x, dict) else "")
    elif 'listing_agent' in df.columns:
        print("Listing agent sample:", df['listing_agent'].iloc[0] if not df['listing_agent'].empty else "No listing agent")
        # Alternative field name
        df['org_agentName'] = df['listing_agent'].apply(lambda x: x.get('name', "") if isinstance(x, dict) else "")
        df['org_agentPhoneNumber'] = df['listing_agent'].apply(lambda x: x.get('phone', "") if isinstance(x, dict) else "")
    elif 'listing_provided_by' in df.columns:
        print("Listing provided by sample:", df['listing_provided_by'].iloc[0] if not df['listing_provided_by'].empty else "No listing provider")
        # For search results format
        df['org_agentName'] = df['listing_provided_by'].apply(lambda x: x.get('name', "") if isinstance(x, dict) else "")
        df['org_agentPhoneNumber'] = df['listing_provided_by'].apply(lambda x: x.get('phone_number', "") if isinstance(x, dict) else "")
    else:
        df['org_agentName'] = ""
        df['org_agentPhoneNumber'] = ""

    # Process agent information if available
    if df['org_agentName'].notna().any():
        df['agentName_split'] = df['org_agentName'].str.split(', ')

        # Extract broker name
        try:
            df['brokerName'] = df['agentName_split'].apply(lambda x: x[0] if isinstance(x, list) and len(x) > 0 else None)
        except:
            df['brokerName'] = ""
    else:
        df['agentName_split'] = None
        df['brokerName'] = ""

    # Process agent phone information if available
    if df['org_agentPhoneNumber'].notna().any():
        df['agentPhoneNumber_split'] = df['org_agentPhoneNumber'].str.split(', ')

        # Extract brokerage phone
        try:
            df['Brokerage Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[0] if isinstance(x, list) and len(x) > 0 else None)
        except:
            df['Brokerage Phone'] = ""
    else:
        df['agentPhoneNumber_split'] = None
        df['Brokerage Phone'] = ""

    # Agent 1 information
    if 'agentName_split' in df.columns and df['agentName_split'].notna().any():
        try:
            df['Agent 1 Name'] = df['agentName_split'].apply(lambda x: x[1] if isinstance(x, list) and len(x) > 1 else None)
        except:
            df['Agent 1 Name'] = ""
    else:
        df['Agent 1 Name'] = ""

    if 'agentPhoneNumber_split' in df.columns and df['agentPhoneNumber_split'].notna().any():
        try:
            df['Agent 1 Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[1] if isinstance(x, list) and len(x) > 1 else None)
        except:
            df['Agent 1 Phone'] = ""
    else:
        df['Agent 1 Phone'] = ""

    # Agent 2 information
    if 'agentName_split' in df.columns and df['agentName_split'].notna().any():
        try:
            df['Agent 2 Name'] = df['agentName_split'].apply(lambda x: x[2] if isinstance(x, list) and len(x) > 2 else None)
        except:
            df['Agent 2 Name'] = ""
    else:
        df['Agent 2 Name'] = ""

    if 'agentPhoneNumber_split' in df.columns and df['agentPhoneNumber_split'].notna().any():
        try:
            df['Agent 2 Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[2] if isinstance(x, list) and len(x) > 2 else None)
        except:
            df['Agent 2 Phone'] = ""
    else:
        df['Agent 2 Phone'] = ""

    # Address processing
    if 'streetAddress' in df.columns:
        df['Sorted Address'] = df['streetAddress'].apply(get_building_name)
        df['Sorted Address'] = df['Sorted Address'].apply(replace_street_types)
        df['Building Number'] = df['streetAddress'].apply(get_building_number)
    else:
        df['Sorted Address'] = ""
        df['Building Number'] = ""

    # Image processing
    if 'photos' in df.columns:
        df['max_links'] = extract_max_width_links(df['photos'])
    else:
        df['max_links'] = [[]]

    # Transform into separate columns
    df_images = df['max_links'].apply(pd.Series)
    if not df_images.empty:
        df_images.columns = [f"image {i+1}" for i in range(df_images.shape[1])]
        # Combine with the original DataFrame
        df_combined = pd.concat([df, df_images], axis=1)
    else:
        df_combined = df

    for col in columns_to_keep:
        if col not in df_combined.columns:
            df_combined[col] = None

    # Filter only available columns from `columns_to_keep`
    available_columns = [col for col in columns_to_keep if col in df_combined.columns]
    filtered_df = df_combined[available_columns]

    # Save to CSV for reference
    output_file = f"individual_{snapshot_id}.csv"
    filtered_df.to_csv(output_file, index=False)
    print(f"Data saved to {output_file}")

    # Clean data for upload
    cleaned_data = filtered_df.fillna("")

    # Upload to Glide Apps
    return upload_to_glideapps(cleaned_data)

def process_next_url_with_snapshot():
    """Process the next URL with a snapshot ID."""
    data = load_urls_json()

    # Find the first unprocessed URL with a snapshot ID
    for i, entry in enumerate(data["urls"]):
        if not entry["is_processed"] and entry["snapshot_id"]:
            url = entry["url"]
            snapshot_id = entry["snapshot_id"]
            print(f"Processing URL: {url} with snapshot ID: {snapshot_id}")

            # Process using the snapshot ID from the JSON file

            # Fetch and process snapshot data
            api_url = f"https://api.brightdata.com/datasets/v3/snapshot/{snapshot_id}?format=json"
            headers = {
                'Authorization': 'Bearer b604b52a-a25f-4cab-ac7e-4df59a05ae12'
            }

            try:
                response = requests.get(api_url, headers=headers)
                response.raise_for_status()

                if isinstance(response.json(), dict):
                    print("Snapshot is still processing. Try again later.")
                    return False
                else:
                    print(f"Data has been processed with {len(response.json())} properties.")
                    success = process_data(response, snapshot_id)

                    if success:
                        # Mark as processed
                        data["urls"][i]["is_processed"] = True
                        data["urls"][i]["processed_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        save_urls_json(data)
                        print(f"URL '{url}' marked as processed.")
                        return True
                    else:
                        print(f"Failed to process URL '{url}'.")
                        return False

            except requests.exceptions.RequestException as e:
                print(f"An error occurred while fetching snapshot data: {e}")
                return False
            except Exception as e:
                print(f"An unexpected error occurred: {e}")
                return False

    print("No unprocessed URLs found with snapshot IDs.")
    return False

def process_all_urls_with_snapshots():
    """Process all URLs with snapshot IDs that haven't been processed yet."""
    data = load_urls_json()
    processed_any = False

    # Find all unprocessed URLs with a snapshot ID
    for i, entry in enumerate(data["urls"]):
        if not entry["is_processed"] and entry["snapshot_id"]:
            url = entry["url"]
            snapshot_id = entry["snapshot_id"]
            print(f"Processing URL: {url} with snapshot ID: {snapshot_id}")

            # Fetch and process snapshot data
            api_url = f"https://api.brightdata.com/datasets/v3/snapshot/{snapshot_id}?format=json"
            headers = {
                'Authorization': 'Bearer 9d6c692a265379ca06c60b4cdff54905180f3dfe6d66594caa55a0b153d0487c'
            }

            try:
                response = requests.get(api_url, headers=headers)
                response.raise_for_status()

                if isinstance(response.json(), dict):
                    print("Snapshot is still processing. Try again later.")
                    continue
                else:
                    print(f"Data has been processed with {len(response.json())} properties.")
                    success = process_data(response, snapshot_id)

                    if success:
                        # Mark as processed
                        data["urls"][i]["is_processed"] = True
                        data["urls"][i]["processed_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        save_urls_json(data)
                        print(f"URL '{url}' marked as processed.")
                        processed_any = True
                    else:
                        print(f"Failed to process URL '{url}'.")

            except requests.exceptions.RequestException as e:
                print(f"An error occurred while fetching snapshot data: {e}")
            except Exception as e:
                print(f"An unexpected error occurred: {e}")

    if not processed_any:
        print("No unprocessed URLs found with snapshot IDs or all snapshots are still processing.")

    return processed_any

def main():
    """Main function."""
    # When run without arguments, process all URLs with snapshot IDs
    print("Checking for URLs with snapshot IDs to process...")
    process_all_urls_with_snapshots()

if __name__ == "__main__":
    main()
