import requests
import pandas as pd

r = requests.post(
    "https://api.glideapp.io/api/function/queryTables",
    headers={"Authorization": "Bearer d394a9c6-e6fc-441b-aecb-602b6b53a0be"},
    json={
        "appID": "YoAncd0fV0YQWzDIqWWO",
        "queries": [
            {
                "tableName": "native-table-0wKs3Tem8p8qUCxM9mNm",
                "utc": True
            }
        ]
    }
)
result = r.json()

main_data = []

# for data in result[]0:
#     print(data)
#     main_data.append(data)

df = pd.DataFrame(result[0])
df = pd.DataFrame(result[0].get("rows"))
print(df)
print(len(result[0]))
df.to_csv("db.csv")
print(result[0].keys())

print(len(result[0].get("rows")))