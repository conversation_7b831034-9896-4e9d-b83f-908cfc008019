import requests
import json

zillow_url = ""
file_path = "url.txt"
output_file = "snapshot_id.txt"

try:
    with open(file_path, "r", encoding="utf8") as f:
        zillow_url = f.readline().strip()  # Read the first line and remove any extra whitespace
except FileNotFoundError:
    print(f"Error: The file '{file_path}' was not found.")
    exit(1)
except Exception as e:
    print(f"An error occurred while reading the file: {e}")
    exit(1)

url = "https://api.brightdata.com/datasets/v3/trigger?dataset_id=gd_lfqkr8wm13ixtbd8f5&include_errors=true&type=discover_new&discover_by=url"

payload = json.dumps([
  {
    "url": zillow_url
  }
])
headers = {
  'Authorization': 'Bearer b604b52a-a25f-4cab-ac7e-4df59a05ae12',
  'Content-Type': 'application/json'
}


try:
    response = requests.post(url, headers=headers, data=payload)
    response.raise_for_status()  # Raise an exception for HTTP errors

    # Parse the response
    response_data = response.json()
    print("Response received:")
    print(json.dumps(response_data, indent=2))  # Pretty-print the JSON response

    # Save snapshot_id to a file
    snapshot_id = response_data.get("snapshot_id")
    if snapshot_id:
        with open(output_file, "w", encoding="utf8") as f:
            f.write(snapshot_id)
        print(f"Snapshot ID '{snapshot_id}' saved to '{output_file}'.")
    else:
        print("Snapshot ID not found in the response.")

except requests.exceptions.RequestException as e:
    print(f"An error occurred while sending the request: {e}")
    exit(1)
except Exception as e:
    print(f"An unexpected error occurred: {e}")
    exit(1)