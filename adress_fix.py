import re
import pandas as pd

# Define the dictionary
street_types = {
    "Street": ["St", "St.", "Street"],
    "Road": ["Rd", "Rd.", "Road"],
    "Avenue": ["Ave", "Ave.", "Avenue"],
    "Boulevard": ["Blvd", "Blvd.", "Boulevard"],
    "Drive": ["Dr", "Dr.", "Drive"],
    "Lane": ["Ln", "Ln.", "Lane"],
    "Court": ["Ct", "Ct.", "Court"],
    "Circle": ["Cir", "Cir.", "Circle"],
    "Terrace": ["Ter", "Ter.", "Terrace"],
    "Place": ["Pl", "Pl.", "Place"],
    "Square": ["Sq", "Sq.", "Square"],
    "Parkway": ["Pkwy", "Pkwy.", "Parkway"],
    "Highway": ["Hwy", "Hwy.", "Highway"],
    "Crescent": ["Cres", "Cres.", "Crescent"],
    "Alley": ["<PERSON>y", "Aly.", "Alley"],
    "Way": ["Way", "Way."],
    "Trail": ["Trl", "Trl.", "Trail"],
    "Path": ["Path", "Path."],
    "Crossing": ["Xing", "Crossing"],
    "Expressway": ["Expy", "Expy.", "Expressway"],
    "Turnpike": ["Tpke", "Tpke.", "Turnpike"],
    "Broadway": ["Broadway"],
    "Plaza": ["Plz", "Plz.", "Plaza"]
}

# Create a mapping of variations to their standardized values
replacement_map = {variation.lower(): standard for standard, variations in street_types.items() for variation in variations}

# Function to replace variations with the proper word
def replace_street_types(address):
    def replacer(match):
        word = match.group()
        return replacement_map.get(word.lower(), word)

    # Use regular expression to match whole words only
    pattern = r'\b(' + '|'.join(re.escape(key) for key in replacement_map.keys()) + r')\b'
    return re.sub(pattern, replacer, address, flags=re.IGNORECASE)

# Example Excel DataFrame
df = pd.read_excel('PropertyMasterExport_Cleaned (1).xlsx')

# Apply the function to the address column
# Get the column index of "Building Address"
building_addr_idx = df.columns.get_loc("Building Address")
# Create standardized address and insert it after Building Address
df.insert(building_addr_idx + 1, "Standardized Address", df["Building Address"].apply(replace_street_types))

# Display the DataFrame
print(df)

df.to_csv('PropertyMasterExport_Cleaned_2.csv', index=False)