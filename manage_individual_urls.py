import sys
import os
import json
import subprocess
from datetime import datetime

# File path
URLS_JSON_FILE = "urls.json"

def load_urls_json():
    """Load the URLs JSON file or create it if it doesn't exist."""
    if not os.path.exists(URLS_JSON_FILE):
        return {"urls": []}

    try:
        with open(URLS_JSON_FILE, "r", encoding="utf8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading URLs JSON file: {e}")
        return {"urls": []}

def save_urls_json(data):
    """Save the URLs JSON file."""
    try:
        with open(URLS_JSON_FILE, "w", encoding="utf8") as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving URLs JSON file: {e}")
        return False

def add_url(url):
    """Add a new URL to the JSON file."""
    data = load_urls_json()

    # Check if URL already exists
    for entry in data["urls"]:
        if entry["url"] == url:
            print(f"URL '{url}' already exists in the JSON file.")
            return False

    # Add new URL
    data["urls"].append({
        "url": url,
        "snapshot_id": None,
        "is_processed": False,
        "added_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "processed_date": None
    })

    return save_urls_json(data)

def list_urls():
    """List all URLs in the JSON file."""
    data = load_urls_json()

    if not data["urls"]:
        print("No URLs found.")
        return

    print("\nURL List:")
    print("-" * 100)
    print(f"{'URL':<60} {'Snapshot ID':<20} {'Processed':<10} {'Added Date':<20}")
    print("-" * 100)

    for entry in data["urls"]:
        print(f"{entry['url']:<60} {entry.get('snapshot_id', 'None'):<20} {str(entry['is_processed']):<10} {entry.get('added_date', 'N/A'):<20}")

def process_all():
    """Process all unprocessed URLs."""
    # First, fetch snapshot IDs for URLs without them
    subprocess.run(["python", "fetch_individual_url.py", "process"])

    # Then, process URLs with snapshot IDs
    subprocess.run(["python", "process_individual_url.py", "process"])

def remove_processed_urls():
    """Remove processed URLs from the JSON file."""
    data = load_urls_json()

    # Filter out processed URLs
    data["urls"] = [entry for entry in data["urls"] if not entry["is_processed"]]

    if save_urls_json(data):
        print("Processed URLs removed successfully.")
    else:
        print("Failed to remove processed URLs.")

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python manage_individual_urls.py add <url>")
        print("  python manage_individual_urls.py list")
        print("  python manage_individual_urls.py process")
        print("  python manage_individual_urls.py clean")
        return

    command = sys.argv[1]

    if command == "add" and len(sys.argv) >= 3:
        url = sys.argv[2]
        if add_url(url):
            print(f"URL '{url}' added successfully.")
        else:
            print(f"Failed to add URL '{url}'.")

    elif command == "list":
        list_urls()

    elif command == "process":
        process_all()

    elif command == "clean":
        remove_processed_urls()

    else:
        print("Invalid command.")
        print("Usage:")
        print("  python manage_individual_urls.py add <url>")
        print("  python manage_individual_urls.py list")
        print("  python manage_individual_urls.py process")
        print("  python manage_individual_urls.py clean")

    # Note: For GitHub Actions, use fetch_individual_url.py and process_individual_url.py directly
    # as they will automatically process all URLs without requiring command-line arguments

if __name__ == "__main__":
    main()
