import requests
import os
import json
import time
import pandas as pd
from datetime import datetime
import re



# File containing the snapshot ID
# snapshot_file = "snapshot_id.txt"
snapshot_file = "snapshot_id.txt"

def load_street_types(file_path="prefix_template.json"):
    """Load street type variations from a JSON file."""
    with open(file_path, "r") as file:
        return json.load(file)
    

def replace_street_types(address):
    street_types = load_street_types()
    replacement_map = {variation.lower(): standard for standard, variations in street_types.items() for variation in variations}
    def replacer(match):
        word = match.group()
        return replacement_map.get(word.lower(), word)

    # Use regular expression to match whole words only
    pattern = r'\b(' + '|'.join(re.escape(key) for key in replacement_map.keys()) + r')\b'
    return re.sub(pattern, replacer, address, flags=re.IGNORECASE)



def load_variations(file_path="building_variations.txt"):
    """Load variations from a text file to match building identifiers."""
    with open(file_path, "r") as file:
        return [line.strip() for line in file.readlines() if line.strip()]


def get_building_name(address, file_path="building_variations.txt"):
    """Extract the building name from an address before keywords."""
    variations = load_variations(file_path)
    pattern = r'|'.join([fr'\b{re.escape(var.lower())}\b' for var in variations])
    # pattern = pattern + "\\s+(.*)"
    # print(pattern)
    
    match = re.split(pattern, address.lower(), maxsplit=1, flags=re.IGNORECASE)
    return match[0].strip() if match else address.strip()


def get_building_number(address, file_path="building_variations.txt"):
    """Extract the building number from an address after keywords."""
    variations = load_variations(file_path)
    pattern = r'(?i)(?:#|' + '|'.join(re.escape(var) for var in variations) + r')\s*([\w\d-]+)'
    print(pattern)
    try:
        match = re.search(pattern, address, re.IGNORECASE)
        print(address, match , match.group(0),match.group(1) )
        return match.group(1)
    except:
        None

def format_price_history(price_history):
    formatted_history = []
    for record in price_history:
        try:
            # Convert timestamp to datetime
            date_str = datetime.utcfromtimestamp(record['time'] / 1000).strftime('%Y-%m-%d')
            
            # Format price as currency
            formatted_price = "${:,.0f}".format(record.get("price", 0))
            
            # Create the formatted output string
            output_str = f"{date_str}: {formatted_price} ({record['event']})"
            formatted_history.append(output_str)
        except Exception as e:
            # print(e)
            pass

    # Return the formatted history as a single string
    return ", \n".join(formatted_history)


def convert_schools_to_format(school_list):
    print(school_list)
    return ', '.join([f"Rating: {school.get('rating')} Name: {school.get('name')}" for school in school_list])


def convert_schools_to_format(school_list):
    # Using get to avoid errors if 'rating' or 'name' keys are missing
    return ', '.join([f"Rating: {school.get('rating', 'N/A')} Name: {school.get('name', 'Unknown')}" for school in school_list])


def extract_appliances(appliances_list):
    for item in appliances_list:
        if item['title'] == 'Appliances':
            appliances = item['values'][0]  # Get the first value in the list
            # Split by comma and remove unwanted words/phrases
            appliances = appliances.replace('Appliances included: ', '').replace('Hot Water: Gas Stand Alone', 'Hot Water: On Demand')
            appliance_list = appliances.split(',')
            return ', '.join([appliance.strip() for appliance in appliance_list])
    return ''

def extract_heating(appliances_list):
    for item in appliances_list:
        if item['title'] == 'Heating':
            appliances = item['values'][0]  # Get the first value in the list
            # Split by comma and remove unwanted words/phrases
            appliances = appliances.replace('Appliances included: ', '').replace('Hot Water: Gas Stand Alone', 'Hot Water: On Demand')
            appliance_list = appliances.split(',')
            return ', '.join([appliance.strip() for appliance in appliance_list])
    return ''

def extract_cooling(appliances_list):
    for item in appliances_list:
        if item['title'] == 'Cooling':
            try:
                appliances = item['values'][0]  # Get the first value in the list
                # Split by comma and remove unwanted words/phrases
                appliances = appliances.replace('Appliances included: ', '').replace('Hot Water: Gas Stand Alone', 'Hot Water: On Demand')
                appliance_list = appliances.split(',')
                return ', '.join([appliance.strip() for appliance in appliance_list])
            except:
                pass
    return ''

def extract_parking_details(property_list):
    if property_list:
        for item in property_list:
            # print(item)
            if item.get('title') == 'Parking':
                parking_features = []
                for value in item['values']:
                    if "Parking features:" in value:
                        # Extract features after the colon
                        features = value.split("Parking features:")[-1].strip()
                        parking_features.extend([feature.strip() for feature in features.split(",")])
                    elif "Has garage:" in value and "Yes" in value:
                        parking_features.append("Garage")
                    elif "Covered spaces:" in value and int(value.split(":")[-1].strip()) > 0:
                        parking_features.append("Covered")
                    elif "Has uncovered spaces:" in value and "Yes" in value:
                        parking_features.append("Uncovered")
                # Return unique and formatted features
                return ', '.join(sorted(set(parking_features)))
    return ''



# Function to extract and format open house details
def format_open_house_details(details_list):
    if not details_list or not isinstance(details_list, list):
        return ""
    formatted_details = []
    for detail in details_list:
        try:
            # Extract fields with default fallbacks
            day = detail.get('open_house_day', '')
            date_iso = detail.get('open_house_date', '')
            start_epoch = detail.get('open_house_start_time', None)
            end_epoch = detail.get('open_house_end_time', None)

            # Convert ISO date to readable format
            date = datetime.fromisoformat(date_iso.replace('Z', '')).strftime('%Y-%m-%d') if date_iso else ""

            # Convert epoch to readable time
            start_time = datetime.fromtimestamp(start_epoch / 1000).strftime('%I:%M %p') if start_epoch else ""
            end_time = datetime.fromtimestamp(end_epoch / 1000).strftime('%I:%M %p') if end_epoch else ""

            # Format the output string
            formatted_details.append(f"{day}, {date}, {start_time} - {end_time}")
        except Exception as e:
            print(f"Error formatting details: {e}")
            continue

    return '; '.join(formatted_details)


def get_first_source(price_history):
    if not price_history or not isinstance(price_history, list):
        return None  # Return None for empty or invalid data
    return price_history[0].get('source', None)  # Extract "source" from the first dictionary


def extract_max_width_links(json_column):
    max_links = []
    for photos in json_column:
        for image in photos:
            print(image.get("mixedSources", {}).get("jpeg" , [])[-1].get("url" , None))
    return max_links


def extract_max_width_links(json_column):
    max_links = []
    for photos in json_column:
        try:
            # Parse JSON if it's a string
            if isinstance(photos, str):
                photos = json.loads(photos)
            # Extract the last image URL from "mixedSources"
            image_links = [
                image.get("mixedSources", {}).get("jpeg", [])[-1].get("url", None)
                for image in photos
                if image.get("mixedSources", {}).get("jpeg", [])
            ]
            max_links.append(image_links)
        except Exception as e:
            max_links.append([])  # Handle errors gracefully
    return max_links


def upload_to_glideapps(cleaned_data):

    # Your API token and table ID
    api_token = "d394a9c6-e6fc-441b-aecb-602b6b53a0be"
    table_id = "4708265f-b64c-4388-bc64-2d93aebda2bf"  # Replace with your actual table ID

    # API endpoint
    url = f"https://api.glideapps.com/tables/{table_id}/rows"

    # Modify column names: Capitalize the first letter
    cleaned_data.columns = [col for col in cleaned_data.columns]
    print(cleaned_data.columns)

    # Prepare the payload from the updated cleaned_data DataFrame
    payload = cleaned_data.to_dict(orient="records")

    # Set headers for the request
    headers = {
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json"
    }

    # Send the POST request
    response = requests.post(url, json=payload, headers=headers)

    # Print the response from the API
    print("Status Code:", response.status_code)
    print("Response Body:", response.text)
    global snapshot_file
    if response.status_code == 201:
        print("removing file")
        os.remove(snapshot_file)
        print(f'file has been deleted {snapshot_file}')

def extract_home_type(construction_json):
    for item in construction_json:
        if item['title'] == 'Type & style':
            for value in item['values']:
                if 'Home type:' in value:
                    return value.split(":")[1].strip()
    return None


def process_data(response):
    current_datetime = datetime.now()

    # Format the date and time in the desired format
    fetchDate = current_datetime.strftime("%d-%m-%Y %I:%M:%S %p")

    # Assuming `response.json()` is a dictionary or list of dictionaries
    df = pd.DataFrame(response.json())


    # List of columns to keep
    # columns_to_keep = [
    # "ZPID", "url", "fetchDate", "streetAddress", "city", "state", "zipcode", 
    # "price", "bedrooms", "bathrooms", "livingArea", "yearBuilt", 
    # "propertyType", "status", "daysOnZillow", "views", "saved", 
    # "priceHistory", "description", "schools", "propertyTaxRate", 
    # "annualHomeownersInsurance", "hoaFee", "appliances", "heating", 
    # "cooling", "parkingFeatures", "openHouseSchedule", "mlsId", 
    # "mlsName", "agentName", "agentPhoneNumber", "coAgentName", 
    # "coAgentNumber", "brokerName", "brokerPhoneNumber", "Brokerage Phone","Agent 1 Name", "Agent 1 Phone","Agent 2 Name", "Agent 2 Phone", "propertyJSON",
    # ]

    
# List of columns to keep
    columns_to_keep = [
    "ZPID", "url", "fetchDate", "streetAddress", "Sorted Address", "Building Number",  "city", "state", "zipcode", 
    "price", "bedrooms", "bathrooms", "livingArea", "yearBuilt", 
    "propertyType", "status", "daysOnZillow", "views", "saved", 
    "priceHistory", "description", "schools", "propertyTaxRate", 
    "annualHomeownersInsurance", "hoaFee", "appliances", "heating", 
    "cooling", "parkingFeatures", "openHouseSchedule", "mlsId", 
    "mlsName", "org_agentName", "org_agentPhoneNumber", "brokerName", "Brokerage Phone","Agent 1 Name", "Agent 1 Phone","Agent 2 Name", "Agent 2 Phone", "propertyJSON",
    ]

    for i in range(39):
        columns_to_keep.append(f"image {i+1}")

    # Filter the DataFrame to keep only these columns
    
    df['ZPID'] = df['zpid']
    df['fetchDate'] = fetchDate
    df["propertyType"] =  df['construction'].apply(extract_home_type)
    df["status"] = df['homeStatus']
    df['views'] = df['overview'].apply(lambda x: x.get('number_of_views', None))
    df['saved'] = df['overview'].apply(lambda x: x.get('number_of_saves', None))
    df['schools'] = df['schools'].apply(convert_schools_to_format)
    df['hoaFee'] = df['hoa_details'].apply(lambda x: x.get('hoa_fee_value', None))
    df['appliances'] = df['interior_full'].apply(extract_appliances)
    df['heating'] = df['interior_full'].apply(extract_heating)
    df['cooling'] = df['interior_full'].apply(extract_cooling)
    try:
        df['parkingFeatures'] = df['property'].apply(extract_parking_details)
    except:
        pass
    df['openHouseSchedule'] = df['open_house_details'].apply(extract_parking_details)
    df['mlsId'] = df['mls_id']
    try:
        df['brokerName'] = df['brokerageName']
    except:
        pass
    df['mlsName'] = df['priceHistory'].apply(get_first_source)
    df['org_agentName'] = df['listing_provided_by'].apply(lambda x: x.get('name', None))
    df['org_agentPhoneNumber'] = df['listing_provided_by'].apply(lambda x: x.get('phone_number', None))
    try:
        df['priceHistory'] = df['priceHistory'].apply(format_price_history)
    except:
        pass


    df['agentName_split'] = df['org_agentName'].str.split(', ')
    df['agentPhoneNumber_split'] = df['org_agentPhoneNumber'].str.split(', ')

    # Extract specific values into new columns
    try:
        df['brokerName'] = df['agentName_split'].apply(lambda x: x[0] if len(x) > 0 else None)
    except:
        pass
    try:
        df['Brokerage Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[0] if len(x) > 0 else None)
    except:
        pass
    try:
        df['Agent 1 Name'] = df['agentName_split'].apply(lambda x: x[1] if len(x) > 1 else None)
    except:
        pass
    try:
        df['Agent 1 Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[1] if len(x) > 1 else None)
    except:
        pass
    try:
        df['Agent 2 Name'] = df['agentName_split'].apply(lambda x: x[2] if len(x) > 2 else None)
    except:
        pass
    try:
        df['Agent 2 Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[2] if len(x) > 2 else None)
    except:
        pass
    try:
        df['Agent 2 Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[2] if len(x) > 2 else None)
    except:
        pass
    df['Sorted Address'] = df['streetAddress'].apply(get_building_name)
    df['Sorted Address'] = df['streetAddress'].apply(replace_street_types)
    df['Building Number'] = df['streetAddress'].apply(get_building_number)
    df['max_links'] = extract_max_width_links(df['photos'])

    # df["propertyJSON"] = response.json()

    # Transform into separate columns
    df_images = df['max_links'].apply(pd.Series)
    df_images.columns = [f"image {i+1}" for i in range(df_images.shape[1])]

    # Combine with the original DataFrame if needed
    df_combined = pd.concat([df, df_images], axis=1)
    
    for col in columns_to_keep:
        if col not in df_combined.columns:
            df_combined[col] = None

    # Filter only available columns from `columns_to_keep`
    available_columns = [col for col in columns_to_keep if col in df_combined.columns]
    filtered_df = df_combined[available_columns]

    # Save the filtered DataFrame to an Excel file
    # filtered_df.to_excel("final sample.xlsx", index=False)


    cleaned_data = filtered_df.where(pd.notnull(filtered_df), None)
    cleaned_data = filtered_df.fillna("")

    assert not cleaned_data.isnull().values.any(), "DataFrame still contains NaN values."
    cleaned_data.to_csv("final.csv", index=False)

    print(cleaned_data.columns)
    upload_to_glideapps(cleaned_data)





def fetch_data():
    try:
        with open(snapshot_file, "r", encoding="utf8") as f:
            snapshot_id = f.read().strip()
    except FileNotFoundError:
        print(f"Error: The file '{snapshot_file}' was not found.")
        exit(1)

    # Bright Data API URL to process the snapshot

    url = f"https://api.brightdata.com/datasets/v3/snapshot/{snapshot_id}?format=json"

    # Set headers
    headers = {
    'Authorization': 'Bearer b604b52a-a25f-4cab-ac7e-4df59a05ae12'
    }


    # Fetch and process snapshot data
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        snapshot_data = response.json()
        # print(snapshot_data)

        # Check the status of the snapshot
        # status = snapshot_data.get("status")
        if isinstance(response.json(), dict):
            print("Snapshot is still processing. Retrying in 10 seconds...")
            time.sleep(10)
        else:
            print(f"Data has been process with {len(snapshot_data)} properties.")
            process_data(response)
            

    except requests.exceptions.RequestException as e:
        print(f"An error occurred while fetching snapshot data: {e}")

    except Exception as e:
        print(f"An unexpected error occurred: {e}")

fetch_data()
