import requests
import json
import os
from datetime import datetime

# File path
URLS_JSON_FILE = "urls.json"

def load_urls_json():
    """Load the URLs JSON file or create it if it doesn't exist."""
    if not os.path.exists(URLS_JSON_FILE):
        return {"urls": []}

    try:
        with open(URLS_JSON_FILE, "r", encoding="utf8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading URLs JSON file: {e}")
        return {"urls": []}

def save_urls_json(data):
    """Save the URLs JSON file."""
    try:
        with open(URLS_JSON_FILE, "w", encoding="utf8") as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving URLs JSON file: {e}")
        return False

def fetch_url_data(url):
    """Fetch data for a URL from Bright Data API."""
    api_url = "https://api.brightdata.com/datasets/v3/trigger?dataset_id=gd_m794g571225l6vm7gh&include_errors=true"

    payload = json.dumps([
        {
            "url": url
        }
    ])

    headers = {
        'Authorization': 'Bearer 9d6c692a265379ca06c60b4cdff54905180f3dfe6d66594caa55a0b153d0487c',
        'Content-Type': 'application/json'
    }

    try:
        response = requests.post(api_url, headers=headers, data=payload)
        response.raise_for_status()

        response_data = response.json()
        print("Response received:")
        print(json.dumps(response_data, indent=2))

        snapshot_id = response_data.get("snapshot_id")
        if snapshot_id:
            print(f"Snapshot ID '{snapshot_id}' received from API.")
            return snapshot_id
        else:
            print("Snapshot ID not found in the response.")
            return None
    except requests.exceptions.RequestException as e:
        print(f"An error occurred while sending the request: {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None

def add_url(url):
    """Add a new URL to the JSON file."""
    data = load_urls_json()

    # Check if URL already exists
    for entry in data["urls"]:
        if entry["url"] == url:
            print(f"URL '{url}' already exists in the JSON file.")
            return False

    # Add new URL
    data["urls"].append({
        "url": url,
        "snapshot_id": None,
        "is_processed": False,
        "added_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "processed_date": None
    })

    return save_urls_json(data)

def process_next_unprocessed_url():
    """Process the next unprocessed URL in the JSON file."""
    data = load_urls_json()

    # Find the first unprocessed URL without a snapshot ID
    for i, entry in enumerate(data["urls"]):
        if not entry["is_processed"] and not entry["snapshot_id"]:
            url = entry["url"]
            print(f"Processing URL: {url}")

            snapshot_id = fetch_url_data(url)
            if snapshot_id:
                data["urls"][i]["snapshot_id"] = snapshot_id
                save_urls_json(data)
                print(f"Snapshot ID '{snapshot_id}' saved for URL '{url}'.")
                return True
            else:
                print(f"Failed to get snapshot ID for URL '{url}'.")
                return False

    print("No unprocessed URLs found without snapshot IDs.")
    return False

def list_urls():
    """List all URLs in the JSON file."""
    data = load_urls_json()

    if not data["urls"]:
        print("No URLs found.")
        return

    print("\nURL List:")
    print("-" * 100)
    print(f"{'URL':<60} {'Snapshot ID':<20} {'Processed':<10} {'Added Date':<20}")
    print("-" * 100)

    for entry in data["urls"]:
        print(f"{entry['url']:<60} {entry.get('snapshot_id', 'None'):<20} {str(entry['is_processed']):<10} {entry.get('added_date', 'N/A'):<20}")

def process_all_unprocessed_urls():
    """Process all unprocessed URLs without snapshot IDs."""
    data = load_urls_json()
    processed_any = False

    # Find all unprocessed URLs without a snapshot ID
    for i, entry in enumerate(data["urls"]):
        if not entry["is_processed"] and not entry["snapshot_id"]:
            url = entry["url"]
            print(f"Processing URL: {url}")

            snapshot_id = fetch_url_data(url)
            if snapshot_id:
                data["urls"][i]["snapshot_id"] = snapshot_id
                save_urls_json(data)
                print(f"Snapshot ID '{snapshot_id}' saved for URL '{url}'.")
                processed_any = True
            else:
                print(f"Failed to get snapshot ID for URL '{url}'.")

    if not processed_any:
        print("No unprocessed URLs found without snapshot IDs.")

    return processed_any

def main():
    """Main function."""
    # When run without arguments, process all unprocessed URLs
    print("Checking for unprocessed URLs...")
    process_all_unprocessed_urls()

if __name__ == "__main__":
    main()
