# Individual URL Processing System

This system allows you to process individual URLs from Zillow (or similar sites) using the Bright Data API, similar to the existing GitHub URL processing system.

## Files

- `urls.json`: Stores the list of URLs to process, along with their processing status
- `fetch_individual_url.py`: <PERSON>ript to fetch snapshot IDs for URLs
- `process_individual_url.py`: Script to process snapshots and upload data to Glide Apps
- `manage_individual_urls.py`: Management script to handle both fetching and processing


## How to Use

### For GitHub Actions Automation

The scripts are designed to work automatically without command-line arguments:

1. **Fetch Snapshot IDs**:
   ```
   python fetch_individual_url.py
   ```
   This will automatically check for any unprocessed URLs without snapshot IDs and fetch snapshot IDs for them.

2. **Process URLs with Snapshot IDs**:
   ```
   python process_individual_url.py
   ```
   This will automatically process any URLs that have snapshot IDs but haven't been processed yet.

### For Manual Use

For manual operations, you can use the management script:

#### Adding a URL

To add a new URL to the system:

```
python manage_individual_urls.py add "https://www.zillow.com/homedetails/123-main-st-city-state-12345/12345_zpid/"
```

#### Listing URLs

To list all URLs in the system:

```
python manage_individual_urls.py list
```

#### Processing URLs

To manually trigger processing of all unprocessed URLs:

```
python manage_individual_urls.py process
```

This will:
1. Fetch snapshot IDs for URLs that don't have them
2. Process URLs that have snapshot IDs but haven't been processed yet

#### Cleaning Up

To remove processed URLs from the system:

```
python manage_individual_urls.py clean
```

## How It Works

1. URLs are stored in `urls.json` with their processing status
2. When a URL is added, it's marked as unprocessed with no snapshot ID
3. When processing, the system first fetches snapshot IDs for unprocessed URLs
4. Then it processes URLs with snapshot IDs and uploads the data to Glide Apps
5. After successful processing, URLs are marked as processed
6. Processed URLs can be removed from the system to keep it clean

## Data Flow

```
Add URL → urls.json → fetch_individual_url.py → Bright Data API →
urls.json (with snapshot ID) → process_individual_url.py →
Glide Apps → urls.json (marked as processed)
```

## GitHub Actions Setup

For GitHub Actions, you can set up two separate workflows:

### 1. Fetch Workflow (runs every few minutes)

```yaml
name: Fetch URL Snapshots

on:
  schedule:
    - cron: '*/5 * * * *'  # Run every 5 minutes

jobs:
  fetch:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install requests
      - name: Fetch snapshot IDs
        run: python fetch_individual_url.py
      - name: Commit changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add urls.json
          git commit -m "Update snapshot IDs" || echo "No changes to commit"
          git push
```

### 2. Process Workflow (runs every few hours)

```yaml
name: Process URL Snapshots

on:
  schedule:
    - cron: '0 */3 * * *'  # Run every 3 hours

jobs:
  process:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install requests pandas
      - name: Process snapshots
        run: python process_individual_url.py
      - name: Commit changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add urls.json
          git commit -m "Update processed URLs" || echo "No changes to commit"
          git push
```
