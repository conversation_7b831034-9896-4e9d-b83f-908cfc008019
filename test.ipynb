{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"snapshot_id\":\"s_m9ft6uex1ve0saerie\"}\n"]}], "source": ["import requests\n", "import json\n", "\n", "url = \"https://api.brightdata.com/datasets/v3/trigger?dataset_id=gd_lfqkr8wm13ixtbd8f5&include_errors=true&type=discover_new&discover_by=url\"\n", "\n", "payload = json.dumps([\n", "  {\n", "    # \"url\": \"https://www.zillow.com/homes/?searchQueryState=%7B%22filterState%22%3A%7B%22isTownhouse%22%3A%7B%22value%22%3Atrue%7D%2C%22isLotLand%22%3A%7B%22value%22%3Atrue%7D%2C%22isManufactured%22%3A%7B%22value%22%3Atrue%7D%2C%22isMultiFamily%22%3A%7B%22value%22%3Atrue%7D%2C%22isApartment%22%3A%7B%22value%22%3Atrue%7D%2C%22isCondo%22%3A%7B%22value%22%3Atrue%7D%2C%22isSingleFamily%22%3A%7B%22value%22%3Atrue%7D%2C%22isApartmentOrCondo%22%3A%7B%22value%22%3Atrue%7D%2C%22isRecentlySold%22%3A%7B%22value%22%3Afalse%7D%2C%22isPreMarketPreForeclosure%22%3A%7B%22value%22%3Afalse%7D%2C%22isPreMarketForeclosure%22%3A%7B%22value%22%3Afalse%7D%2C%22isForRent%22%3A%7B%22value%22%3Afalse%7D%2C%22isForSaleByAgent%22%3A%7B%22value%22%3Atrue%7D%2C%22isForSaleByOwner%22%3A%7B%22value%22%3Atrue%7D%2C%22isAuction%22%3A%7B%22value%22%3Atrue%7D%2C%22isComingSoon%22%3A%7B%22value%22%3Atrue%7D%2C%22isForSaleForeclosure%22%3A%7B%22value%22%3Atrue%7D%2C%22isNewConstruction%22%3A%7B%22value%22%3Atrue%7D%2C%22sortSelection%22%3A%7B%22value%22%3A%22globalrelevanceex%22%7D%7D%2C%22regionSelection%22%3A%5B%7B%22regionId%22%3A273757%7D%5D%2C%22usersSearchTerm%22%3A%22Forest%20Hills%20NY%20homes%22%7D\"\n", "    \"url\": \"https://www.zillow.com/homedetails/69-60-108th-St-308-Forest-Hills-NY-11375/2058889442_zpid/?utm_campaign=iosappmessage&utm_medium=referral&utm_source=txtshare\"\n", "  }\n", "])\n", "headers = {\n", "  'Authorization': '<PERSON><PERSON> c26bc486-6107-4b37-b818-0fce9d7be98e',\n", "  'Content-Type': 'application/json'\n", "}\n", "\n", "response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "\n", "print(response.text)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No match found\n"]}], "source": ["import re\n", "\n", "address = \"105 wall street UNITs 506\"\n", "match = re.search(r'\\bunit\\b\\s+(.*)', address, re.IGNORECASE)\n", "if match:\n", "    result = match.group(0)\n", "    print(result)  # Output: unit 506\n", "else:\n", "    print(\"No match found\")\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Snapshot is empty\n"]}], "source": ["import requests\n", "\n", "# url = \"https://api.brightdata.com/datasets/v3/snapshot/s_m367f1jw2fc9nhho9j?format=json\"\n", "url = \"https://api.brightdata.com/datasets/v3/snapshot/s_m8itqafx1muocixibd?format=json\"\n", "\n", "payload = {}\n", "headers = {\n", "  'Authorization': '<PERSON><PERSON> c26bc486-6107-4b37-b818-0fce9d7be98e'\n", "}\n", "\n", "response = requests.request(\"GET\", url, headers=headers, data=payload)\n", "print(response.text)\n", "# print(response.json()[0])\n", "\n", "# {\"status\":\"running\",\"message\":\"<PERSON><PERSON><PERSON> is not ready yet, try again in 10s\"}\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["83"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["data = response.json()\n", "len(data)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.<PERSON><PERSON><PERSON><PERSON>(response.json())\n", "# df.columns\n", "# df.to_csv(\"121.csv\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4656\\3820735651.py:29: DeprecationWarning: datetime.datetime.utcfromtimestamp() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.fromtimestamp(timestamp, datetime.UTC).\n", "  date_str = datetime.utcfromtimestamp(record['time'] / 1000).strftime('%Y-%m-%d')\n"]}, {"data": {"text/plain": ["Index(['ZPID', 'url', 'fetchDate', 'streetAddress', 'Sorted Address',\n", "       'Building Number', 'city', 'state', 'zipcode', 'price', 'bedrooms',\n", "       'bathrooms', 'livingArea', 'yearBuilt', 'propertyType', 'status',\n", "       'daysOnZillow', 'views', 'saved', 'priceHistory', 'description',\n", "       'schools', 'propertyTaxRate', 'annualHomeownersInsurance', 'hoaFee',\n", "       'appliances', 'heating', 'cooling', 'parkingFeatures',\n", "       'openHouseSchedule', 'mlsId', 'mlsName', 'org_agentName',\n", "       'org_agentPhoneNumber', 'brokerName', 'Brokerage Phone', 'Agent 1 Name',\n", "       'Agent 1 Phone', 'Agent 2 Name', 'Agent 2 Phone', 'propertyJSON',\n", "       'image 1', 'image 2', 'image 3', 'image 4', 'image 5', 'image 6',\n", "       'image 7', 'image 8', 'image 9', 'image 10', 'image 11', 'image 12',\n", "       'image 13', 'image 14', 'image 15', 'image 16', 'image 17', 'image 18',\n", "       'image 19', 'image 20', 'image 21', 'image 22', 'image 23', 'image 24',\n", "       'image 25', 'image 26', 'image 27', 'image 28', 'image 29', 'image 30',\n", "       'image 31', 'image 32', 'image 33', 'image 34', 'image 35', 'image 36',\n", "       'image 37', 'image 38', 'image 39', 'image 40', 'image 41', 'image 42',\n", "       'image 43', 'image 44', 'image 45', 'image 46', 'image 47', 'image 48',\n", "       'image 49', 'image 50'],\n", "      dtype='object')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "from datetime import datetime\n", "import json\n", "import re\n", "\n", "\n", "\n", "def get_building_name(address):\n", "    # Split the address by '#' or 'APT' and return the first part\n", "    building_name = re.split(r'#|APT', address)[0].strip()\n", "    return building_name\n", "\n", "def get_building_number(address):\n", "    # Split the address by '#' or 'APT' and return the first part\n", "    temp_list = re.split(r'#|APT', address)\n", "    if len(temp_list) > 1:\n", "        building_number = temp_list[-1].strip()\n", "    else:\n", "        building_number = None\n", "    return building_number\n", "\n", "\n", "\n", "def format_price_history(price_history):\n", "    formatted_history = []\n", "    for record in price_history:\n", "        try:\n", "            # Convert timestamp to datetime\n", "            date_str = datetime.utcfromtimestamp(record['time'] / 1000).strftime('%Y-%m-%d')\n", "            \n", "            # Format price as currency\n", "            formatted_price = \"${:,.0f}\".format(record.get(\"price\", 0))\n", "            \n", "            # Create the formatted output string\n", "            output_str = f\"{date_str}: {formatted_price} ({record['event']})\"\n", "            formatted_history.append(output_str)\n", "        except Exception as e:\n", "            # print(e)\n", "            pass\n", "\n", "    # Return the formatted history as a single string\n", "    return \", \\n\".join(formatted_history)\n", "\n", "\n", "def convert_schools_to_format(school_list):\n", "    print(school_list)\n", "    return ', '.join([f\"Rating: {school,get('rating')} Name: {school.get('name')}\" for school in school_list])\n", "\n", "\n", "def convert_schools_to_format(school_list):\n", "    # Using get to avoid errors if 'rating' or 'name' keys are missing\n", "    return ', '.join([f\"Rating: {school.get('rating', 'N/A')} Name: {school.get('name', 'Unknown')}\" for school in school_list])\n", "\n", "\n", "def extract_appliances(appliances_list):\n", "    for item in appliances_list:\n", "        if item['title'] == 'Appliances':\n", "            appliances = item['values'][0]  # Get the first value in the list\n", "            # Split by comma and remove unwanted words/phrases\n", "            appliances = appliances.replace('Appliances included: ', '').replace('Hot Water: Gas Stand Alone', 'Hot Water: On Demand')\n", "            appliance_list = appliances.split(',')\n", "            return ', '.join([appliance.strip() for appliance in appliance_list])\n", "    return ''\n", "\n", "def extract_heating(appliances_list):\n", "    for item in appliances_list:\n", "        if item['title'] == 'Heating':\n", "            appliances = item['values'][0]  # Get the first value in the list\n", "            # Split by comma and remove unwanted words/phrases\n", "            appliances = appliances.replace('Appliances included: ', '').replace('Hot Water: Gas Stand Alone', 'Hot Water: On Demand')\n", "            appliance_list = appliances.split(',')\n", "            return ', '.join([appliance.strip() for appliance in appliance_list])\n", "    return ''\n", "\n", "def extract_cooling(appliances_list):\n", "    for item in appliances_list:\n", "        if item['title'] == 'Cooling':\n", "            try:\n", "                appliances = item['values'][0]  # Get the first value in the list\n", "                # Split by comma and remove unwanted words/phrases\n", "                appliances = appliances.replace('Appliances included: ', '').replace('Hot Water: Gas Stand Alone', 'Hot Water: On Demand')\n", "                appliance_list = appliances.split(',')\n", "                return ', '.join([appliance.strip() for appliance in appliance_list])\n", "            except:\n", "                pass\n", "    return ''\n", "\n", "def extract_parking_details(property_list):\n", "    if property_list:\n", "        for item in property_list:\n", "            # print(item)\n", "            if item.get('title') == 'Parking':\n", "                parking_features = []\n", "                for value in item['values']:\n", "                    if \"Parking features:\" in value:\n", "                        # Extract features after the colon\n", "                        features = value.split(\"Parking features:\")[-1].strip()\n", "                        parking_features.extend([feature.strip() for feature in features.split(\",\")])\n", "                    elif \"Has garage:\" in value and \"Yes\" in value:\n", "                        parking_features.append(\"Garage\")\n", "                    elif \"Covered spaces:\" in value and int(value.split(\":\")[-1].strip()) > 0:\n", "                        parking_features.append(\"Covered\")\n", "                    elif \"Has uncovered spaces:\" in value and \"Yes\" in value:\n", "                        parking_features.append(\"Uncovered\")\n", "                # Return unique and formatted features\n", "                return ', '.join(sorted(set(parking_features)))\n", "    return ''\n", "\n", "\n", "\n", "# Function to extract and format open house details\n", "def format_open_house_details(details_list):\n", "    if not details_list or not isinstance(details_list, list):\n", "        return \"\"\n", "    formatted_details = []\n", "    for detail in details_list:\n", "        try:\n", "            # Extract fields with default fallbacks\n", "            day = detail.get('open_house_day', '')\n", "            date_iso = detail.get('open_house_date', '')\n", "            start_epoch = detail.get('open_house_start_time', None)\n", "            end_epoch = detail.get('open_house_end_time', None)\n", "\n", "            # Convert ISO date to readable format\n", "            date = datetime.fromisoformat(date_iso.replace('Z', '')).strftime('%Y-%m-%d') if date_iso else \"\"\n", "\n", "            # Convert epoch to readable time\n", "            start_time = datetime.fromtimestamp(start_epoch / 1000).strftime('%I:%M %p') if start_epoch else \"\"\n", "            end_time = datetime.fromtimestamp(end_epoch / 1000).strftime('%I:%M %p') if end_epoch else \"\"\n", "\n", "            # Format the output string\n", "            formatted_details.append(f\"{day}, {date}, {start_time} - {end_time}\")\n", "        except Exception as e:\n", "            print(f\"Error formatting details: {e}\")\n", "            continue\n", "\n", "    return '; '.join(formatted_details)\n", "\n", "\n", "def get_first_source(price_history):\n", "    if not price_history or not isinstance(price_history, list):\n", "        return None  # Return None for empty or invalid data\n", "    return price_history[0].get('source', None)  # Extract \"source\" from the first dictionary\n", "\n", "\n", "def extract_max_width_links(json_column):\n", "    max_links = []\n", "    for photos in json_column:\n", "        for image in photos:\n", "            print(image.get(\"mixedSources\", {}).get(\"jpeg\" , [])[-1].get(\"url\" , None))\n", "    return max_links\n", "\n", "\n", "def extract_max_width_links(json_column):\n", "    max_links = []\n", "    for photos in json_column:\n", "        try:\n", "            # Parse JSON if it's a string\n", "            if isinstance(photos, str):\n", "                photos = json.loads(photos)\n", "            # Extract the last image URL from \"mixedSources\"\n", "            image_links = [\n", "                image.get(\"mixedSources\", {}).get(\"jpeg\", [])[-1].get(\"url\", None)\n", "                for image in photos\n", "                if image.get(\"mixedSources\", {}).get(\"jpeg\", [])\n", "            ]\n", "            max_links.append(image_links)\n", "        except Exception as e:\n", "            max_links.append([])  # Handle errors gracefully\n", "    return max_links\n", "\n", "\n", "def extract_home_type(construction_json):\n", "    for item in construction_json:\n", "        if item['title'] == 'Type & style':\n", "            for value in item['values']:\n", "                if 'Home type:' in value:\n", "                    return value.split(\":\")[1].strip()\n", "    return None\n", "\n", "\n", "def get_building_name(address):\n", "    # Split the address by '#' or 'APT' and return the first part\n", "    building_name = re.split(r'#|APT', address)[0].strip()\n", "    return building_name\n", "\n", "\n", "# Get the current date and time\n", "current_datetime = datetime.now()\n", "\n", "# Format the date and time in the desired format\n", "fetchDate = current_datetime.strftime(\"%d-%m-%Y %I:%M:%S %p\")\n", "\n", "\n", "\n", "\n", "# Assuming `response.json()` is a dictionary or list of dictionaries\n", "df = pd.<PERSON><PERSON><PERSON><PERSON>(response.json())\n", "\n", "# List of columns to keep\n", "columns_to_keep = [\n", "    \"ZPID\", \"url\", \"fetchDate\", \"streetAddress\", \"Sorted Address\", \"Building Number\",  \"city\", \"state\", \"zipcode\", \n", "    \"price\", \"bedrooms\", \"bathrooms\", \"livingArea\", \"yearBuilt\", \n", "    \"propertyType\", \"status\", \"daysOnZillow\", \"views\", \"saved\", \n", "    \"priceHistory\", \"description\", \"schools\", \"propertyTaxRate\", \n", "    \"annualHomeownersInsurance\", \"hoaFee\", \"appliances\", \"heating\", \n", "    \"cooling\", \"parkingFeatures\", \"openHouseSchedule\", \"mlsId\", \n", "    \"mlsName\", \"org_agentName\", \"org_agentPhoneNumber\", \"brokerName\", \"Brokerage Phone\",\"Agent 1 Name\", \"Agent 1 Phone\",\"Agent 2 Name\", \"Agent 2 Phone\", \"propertyJSON\",\n", "]\n", "\n", "for i in range(50):\n", "    columns_to_keep.append(f\"image {i+1}\")\n", "\n", "# Filter the DataFrame to keep only these columns\n", "df['ZPID'] = df['zpid']\n", "df['fetchDate'] = fetchDate\n", "df[\"propertyType\"] =  df['construction'].apply(extract_home_type)\n", "df[\"status\"] = df['homeStatus']\n", "df['views'] = df['overview'].apply(lambda x: x.get('number_of_views', None))\n", "df['saved'] = df['overview'].apply(lambda x: x.get('number_of_saves', None))\n", "df['schools'] = df['schools'].apply(convert_schools_to_format)\n", "df['hoaFee'] = df['hoa_details'].apply(lambda x: x.get('hoa_fee_value', None))\n", "df['appliances'] = df['interior_full'].apply(extract_appliances)\n", "df['heating'] = df['interior_full'].apply(extract_heating)\n", "df['cooling'] = df['interior_full'].apply(extract_cooling)\n", "try:\n", "    df['parkingFeatures'] = df['property'].apply(extract_parking_details)\n", "except:\n", "    pass\n", "df['openHouseSchedule'] = df['open_house_details'].apply(extract_parking_details)\n", "df['mlsId'] = df['mls_id']\n", "try:\n", "    df['brokerName'] = df['brokerageName']\n", "except:\n", "    pass\n", "df['mlsName'] = df['priceHistory'].apply(get_first_source)\n", "df['org_agentName'] = df['listing_provided_by'].apply(lambda x: x.get('name', None))\n", "df['org_agentPhoneNumber'] = df['listing_provided_by'].apply(lambda x: x.get('phone_number', None))\n", "try:\n", "    df['priceHistory'] = df['priceHistory'].apply(format_price_history)\n", "except:\n", "    pass\n", "\n", "\n", "df['agentName_split'] = df['org_agentName'].str.split(', ')\n", "df['agentPhoneNumber_split'] = df['org_agentPhoneNumber'].str.split(', ')\n", "\n", "# Extract specific values into new columns\n", "try:\n", "    df['brokerName'] = df['agentName_split'].apply(lambda x: x[0] if len(x) > 0 else None)\n", "except:\n", "    pass\n", "try:\n", "    df['Brokerage Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[0] if len(x) > 0 else None)\n", "except:\n", "    pass\n", "try:\n", "    df['Agent 1 Name'] = df['agentName_split'].apply(lambda x: x[1] if len(x) > 1 else None)\n", "except:\n", "    pass\n", "try:\n", "    df['Agent 1 Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[1] if len(x) > 1 else None)\n", "except:\n", "    pass\n", "try:\n", "    df['Agent 2 Name'] = df['agentName_split'].apply(lambda x: x[2] if len(x) > 2 else None)\n", "except:\n", "    pass\n", "try:\n", "    df['Agent 2 Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[2] if len(x) > 2 else None)\n", "except:\n", "    pass\n", "try:\n", "    df['Agent 2 Phone'] = df['agentPhoneNumber_split'].apply(lambda x: x[2] if len(x) > 2 else None)\n", "except:\n", "    pass\n", "# df[\"propertyJSON\"] = response.json()\n", "\n", "df['Home type'] = df['construction'].apply(extract_home_type)\n", "df['Sorted Address'] = df['streetAddress'].apply(get_building_name)\n", "df['Building Number'] = df['streetAddress'].apply(get_building_number)\n", "df['max_links'] = extract_max_width_links(df['photos'])\n", "\n", "# Transform into separate columns\n", "df_images = df['max_links'].apply(pd.Series)\n", "df_images.columns = [f\"image {i+1}\" for i in range(df_images.shape[1])]\n", "\n", "# Combine with the original DataFrame if needed\n", "df_combined = pd.concat([df, df_images], axis=1)\n", "\n", "for col in columns_to_keep:\n", "    if col not in df_combined.columns:\n", "        df_combined[col] = None\n", "\n", "# Filter only available columns from `columns_to_keep`\n", "available_columns = [col for col in columns_to_keep if col in df_combined.columns]\n", "filtered_df = df_combined[available_columns]\n", "\n", "# Save the filtered DataFrame to an Excel file\n", "# filtered_df.to_excel(\"final sample.xlsx\", index=False)\n", "\n", "filtered_df\n", "# # Transform into separate columns\n", "# df_images = df['max_links'].apply(pd.Series)\n", "# df_images.columns = [f\"image {i+1}\" for i in range(df_images.shape[1])]\n", "\n", "# # Combine with the original DataFrame if needed\n", "# df_combined = pd.concat([df, df_images], axis=1)\n", "\n", "\n", "\n", "\n", "# available_columns = [col for col in columns_to_keep if col in df_combined.columns]\n", "# filtered_df = df_combined[available_columns]\n", "\n", "# # Display the filtered DataFrame\n", "# print(filtered_df.columns)\n", "# filtered_df = df.reindex(columns=columns_to_keep)\n", "\n", "\n", "\n", "# filtered_df.to_excel(\"12.xlsx\", index=False)\n", "\n", "# cleaned_data = filtered_df.where(pd.notnull(filtered_df), None)\n", "cleaned_data = filtered_df.fillna(\"\")\n", "\n", "assert not cleaned_data.isnull().values.any(), \"DataFrame still contains NaN values.\"\n", "\n", "cleaned_data.to_csv(\"final.csv\", index=False)\n", "cleaned_data.columns\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["'84-22 60th Road'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import re\n", "def get_building_name(address):\n", "    # Split the address by '#' or 'APT' and return the first part\n", "    building_name = re.split(r'#|APT', address)[0].strip()\n", "    return building_name\n", "\n", "\n", "address =   \"84-22 60th Road #154\"\n", "\n", "\n", "get_building_name(address)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "# Prepare mutations\n", "mutations = []\n", "for _, row in cleaned_data.iterrows():\n", "    mutations.append({\n", "        \"kind\": \"add-row-to-table\",\n", "        \"tableName\": \"native-table-0wKs3Tem8p8qUCxM9mNm\",\n", "        \"columnValues\": {\n", "            \"IRwYR\": row.get(\"ZPID\", \"\"),\n", "            \"O1QQv\": row.get(\"url\", \"\"),\n", "            \"mS8Ev\": row.get(\"fetchDate\", \"\"),\n", "            \"VakKG\": row.get(\"streetAddress\", \"\"),\n", "            \"ZK06O\": row.get(\"city\", \"\"),\n", "            \"JVZCh\": row.get(\"state\", \"\"),\n", "            \"PBdJz\": row.get(\"zipcode\", \"\"),\n", "            \"o9Ari\": row.get(\"price\", \"\"),\n", "            \"Ef9o9\": row.get(\"bedrooms\", \"\"),\n", "            \"ha57f\": row.get(\"bathrooms\", \"\"),\n", "            \"V3gAW\": row.get(\"livingArea\", \"\"),\n", "            \"bx6Mi\": row.get(\"yearBuilt\", \"\"),\n", "            \"husq1\": row.get(\"propertyType\", \"\"),\n", "            \"ZoLHe\": row.get(\"status\", \"\"),\n", "            \"KI6z0\": row.get(\"daysOnZillow\", \"\"),\n", "            \"2PdRJ\": row.get(\"views\", \"\"),\n", "            \"qaWnI\": row.get(\"saved\", \"\"),\n", "            \"Czyzt\": row.get(\"priceHistory\", \"\"),\n", "            \"02WNw\": row.get(\"description\", \"\"),\n", "            \"AAEyq\": row.get(\"schools\", \"\"),\n", "            \"n6nEe\": row.get(\"propertyTaxRate\", \"\"),\n", "            \"meRG2\": row.get(\"annualHomeownersInsurance\", \"\"),\n", "            \"CCHAn\": row.get(\"hoaFee\", \"\"),\n", "            \"eaBIJ\": row.get(\"appliances\", \"\"),\n", "            \"JZQ3h\": row.get(\"heating\", \"\"),\n", "            \"CUUUu\": row.get(\"cooling\", \"\"),\n", "            \"Ek8Fs\": row.get(\"parkingFeatures\", \"\"),\n", "            \"PdNir\": row.get(\"openHouseSchedule\", \"\"),\n", "            \"1AvkZ\": row.get(\"mlsId\", \"\"),\n", "            \"z4ZdM\": row.get(\"mlsName\", \"\"),\n", "            \"naDNL\": row.get(\"agentName\", \"\"),\n", "            \"791Dl\": row.get(\"agentPhoneNumber\", \"\"),\n", "            \"m8Atd\": row.get(\"coAgentName\", \"\"),\n", "            \"xeYiw\": row.get(\"coAgentNumber\", \"\"),\n", "            \"bTo3l\": row.get(\"brokerName\", \"\"),\n", "            \"RH9Vu\": row.get(\"brokerPhoneNumber\", \"\"),\n", "            \"zS4LH\": row.get(\"propertyJSON\", \"\"),\n", "            \"7wlhT\": row.get(\"image 1\", \"\"),\n", "            \"wz3bk\": row.get(\"image 2\", \"\"),\n", "            \"Vs3JD\": row.get(\"image 3\", \"\"),\n", "            \"4Uqtg\": row.get(\"image 4\", \"\"),\n", "            \"dblte\": row.get(\"image 5\", \"\"),\n", "            # Add more images dynamically if necessary\n", "        }\n", "    })\n", "\n", "# API request payload\n", "payload = {\n", "    \"appID\": \"YoAncd0fV0YQWzDIqWWO\",\n", "    \"mutations\": mutations\n", "}\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["201\n", "{\"data\":{\"tableID\":\"4708265f-b64c-4388-bc64-2d93aebda2bf\",\"rowIDs\":[]}}\n"]}], "source": ["import requests\n", "\n", "# Your API token\n", "api_token = \"d394a9c6-e6fc-441b-aecb-602b6b53a0be\"\n", "\n", "# API endpoint\n", "url = \"https://api.glideapps.com/tables\"\n", "\n", "# Define the table name\n", "table_name = \"MasterPropertyList_v1_final\"\n", "\n", "# Generate the schema from the DataFrame columns\n", "columns = [\n", "    {\n", "        \"id\": column,\n", "        \"displayName\": column.replace(\"_\", \" \").capitalize(),\n", "        \"type\": \"string\"  # Setting all columns to string type\n", "    }\n", "    for column in cleaned_data.columns\n", "]\n", "\n", "# Create the payload\n", "payload = {\n", "    \"name\": table_name,\n", "    \"schema\": {\"columns\": columns},\n", "    \"rows\": []  # No rows to add initially; add rows later if needed\n", "}\n", "\n", "# Set headers for the request\n", "headers = {\n", "    \"Authorization\": f\"Bearer {api_token}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "# Make the POST request to create the table\n", "response = requests.post(url, json=payload, headers=headers)\n", "\n", "# Print the response from the API\n", "print(response.status_code)\n", "print(response.text)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['ZPID', 'url', 'fetchDate', 'streetAddress', 'Sorted Address', 'city',\n", "       'state', 'zipcode', 'price', 'bedrooms', 'bathrooms', 'livingArea',\n", "       'yearBuilt', 'propertyType', 'status', 'daysOnZillow', 'views', 'saved',\n", "       'priceHistory', 'description', 'schools', 'propertyTaxRate',\n", "       'annualHomeownersInsurance', 'hoaFee', 'appliances', 'heating',\n", "       'cooling', 'parkingFeatures', 'openHouseSchedule', 'mlsId', 'mlsName',\n", "       'org_agentName', 'org_agentPhoneNumber', 'brokerName',\n", "       'Brokerage Phone', 'Agent 1 Name', 'Agent 1 Phone', 'Agent 2 Name',\n", "       'Agent 2 Phone', 'propertyJSON', 'image 1', 'image 2', 'image 3',\n", "       'image 4', 'image 5', 'image 6', 'image 7', 'image 8', 'image 9',\n", "       'image 10', 'image 11', 'image 12', 'image 13', 'image 14', 'image 15',\n", "       'image 16', 'image 17', 'image 18', 'image 19', 'image 20', 'image 21',\n", "       'image 22', 'image 23', 'image 24', 'image 25', 'image 26', 'image 27',\n", "       'image 28', 'image 29', 'image 30', 'image 31', 'image 32', 'image 33',\n", "       'image 34', 'image 35', 'image 36', 'image 37', 'image 38', 'image 39',\n", "       'image 40', 'image 41', 'image 42', 'image 43', 'image 44', 'image 45',\n", "       'image 46', 'image 47', 'image 48', 'image 49', 'image 50'],\n", "      dtype='object')\n", "Status Code: 201\n", "Response Body: {\"data\":{\"rowIDs\":[\"5dILp8RITYazAOpZoduOFg\",\"AuAzCyonR8yMw5EpVwYaLw\",\"KOs0K4f1QIaEWYO58ZQhvA\",\"UmPh0YOTSVCKqB77D8vpyg\",\"kMIn1o9zTRqFWmn.-o.syw\",\"7R102dalTLatHC5KyP5i.g\",\"a7Fv2YeNSiGOMvu40WCNsg\"]}}\n"]}], "source": ["import requests\n", "\n", "# Your API token and table ID\n", "api_token = \"d394a9c6-e6fc-441b-aecb-602b6b53a0be\"\n", "table_id = \"15cbba3c-7758-4782-a48f-997bfaf1fe6e\"  # Replace with your actual table ID\n", "table_id = \"32dec784-9a3e-4461-8195-4e5077ba9648\"  # Replace with your actual table ID\n", "\n", "# API endpoint\n", "url = f\"https://api.glideapps.com/tables/{table_id}/rows\"\n", "\n", "# Modify column names: Capitalize the first letter\n", "cleaned_data.columns = [col for col in cleaned_data.columns]\n", "print(cleaned_data.columns)\n", "\n", "# Prepare the payload from the updated cleaned_data DataFrame\n", "payload = cleaned_data.to_dict(orient=\"records\")\n", "\n", "# Set headers for the request\n", "headers = {\n", "    \"Authorization\": f\"Bearer {api_token}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "# Send the POST request\n", "response = requests.post(url, json=payload, headers=headers)\n", "\n", "# Print the response from the API\n", "print(\"Status Code:\", response.status_code)\n", "print(\"Response Body:\", response.text)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Failed to upload data. Status Code: 400\n", "Response: Bad Request\n"]}], "source": ["# Define API endpoint and headers\n", "url = \"https://api.glideapp.io/api/function/mutateTables\"\n", "headers = {\n", "    \"Authorization\": \"Bearer d394a9c6-e6fc-441b-aecb-602b6b53a0be\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "# Make the POST request\n", "response = requests.post(url, headers=headers, json=payload)\n", "\n", "# Check the response\n", "if response.status_code == 200:\n", "    print(\"Data uploaded successfully!\")\n", "else:\n", "    print(f\"Failed to upload data. Status Code: {response.status_code}\")\n", "    print(f\"Response: {response.text}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "url = \"https://api.glideapps.com/tables\"\n", "\n", "payload = {\n", "    \"name\": \"Invoices\",\n", "    \"schema\": {\"columns\": [\n", "            {\n", "                \"id\": \"fullName\",\n", "                \"displayName\": \"Full Name\",\n", "                \"type\": \"string\"\n", "            },\n", "            {\n", "                \"id\": \"invoiceDate\",\n", "                \"displayName\": \"Invoice Date\",\n", "                \"type\": \"dateTime\"\n", "            },\n", "            {\n", "                \"id\": \"totalAmount\",\n", "                \"displayName\": \"Total\",\n", "                \"type\": \"number\"\n", "            },\n", "            {\n", "                \"id\": \"amountPaid\",\n", "                \"displayName\": \"Paid\",\n", "                \"type\": \"number\"\n", "            }\n", "        ]},\n", "    \"rows\": [\n", "        {\n", "            \"fullName\": \"<PERSON> Bard\",\n", "            \"invoiceDate\": \"2024-07-29T14:04:15.561Z\",\n", "            \"totalAmount\": 34.5,\n", "            \"amountPaid\": 0\n", "        },\n", "        {\n", "            \"fullName\": \"<PERSON>\",\n", "            \"invoiceDate\": \"2023-06-15T10:30:00.000Z\",\n", "            \"totalAmount\": 50.75,\n", "            \"amountPaid\": 20\n", "        }\n", "    ]\n", "}\n", "headers = {\n", "    \"Authorization\": \"Bearer d394a9c6-e6fc-441b-aecb-602b6b53a0be\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "response = requests.request(\"POST\", url, json=payload, headers=headers)\n", "\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"data\":{\"rowIDs\":[\"zTIlwnnbReWEozJ9dZ.R3w\",\"a.mMx3RubQ6mN6lcGdZ7STQ\"]}}\n"]}], "source": ["import requests\n", "\n", "url = \"https://api.glideapps.com/tables/8c84d844-f065-4d78-beff-259527edba76\"\n", "\n", "payload = {\n", "    \"schema\": {\"columns\": [\n", "            {\n", "                \"id\": \"annualHomeownersInsurance\",\n", "                \"displayName\": \"annualHomeownersInsurance\",\n", "                \"type\": \"string\"\n", "            },\n", "            {\n", "                \"id\": \"invoiceDate\",\n", "                \"displayName\": \"Invoice Date\",\n", "                \"type\": \"dateTime\"\n", "            },\n", "            {\n", "                \"id\": \"totalAmount\",\n", "                \"displayName\": \"Total\",\n", "                \"type\": \"number\"\n", "            },\n", "            {\n", "                \"id\": \"amountPaid\",\n", "                \"displayName\": \"Paid\",\n", "                \"type\": \"number\"\n", "            }\n", "        ]},\n", "    \"rows\": [\n", "        {\n", "            \"fullName\": \"<PERSON> Bard\",\n", "            \"invoiceDate\": \"2024-07-29T14:04:15.561Z\",\n", "            \"totalAmount\": 34.5,\n", "            \"amountPaid\": 0\n", "        },\n", "        {\n", "            \"fullName\": \"<PERSON>\",\n", "            \"invoiceDate\": \"2023-06-15T10:30:00.000Z\",\n", "            \"totalAmount\": 50.75,\n", "            \"amountPaid\": 20\n", "        }\n", "    ]\n", "}\n", "headers = {\n", "    \"Authorization\": \"Bearer d394a9c6-e6fc-441b-aecb-602b6b53a0be\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "response = requests.request(\"PUT\", url, json=payload, headers=headers)\n", "\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "# Your API token and table ID\n", "api_token = \"d394a9c6-e6fc-441b-aecb-602b6b53a0be\"\n", "table_id = \"48dd7320-dde2-4620-a72b-edae4257e3d6\"  # Replace with your actual table ID\n", "\n", "# API endpoint\n", "url = f\"https://api.glideapps.com/tables/{table_id}/rows\"\n", "\n", "# Prepare the payload from cleaned_data DataFrame\n", "payload = cleaned_data.to_dict(orient=\"records\")\n", "\n", "# Set headers for the request\n", "headers = {\n", "    \"Authorization\": f\"Bearer {api_token}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "# Send the POST request\n", "response = requests.post(url, json=payload, headers=headers)\n", "\n", "# Print the response from the API\n", "print(\"Status Code:\", response.status_code)\n", "print(\"Response Body:\", response.text)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "url = \"https://api.glideapps.com/tables/48dd7320-dde2-4620-a72b-edae4257e3d6/rows\"\n", "\n", "payload = [\n", "    {\n", "        \"fullName\": \"Alex Bard2\",\n", "        \"invoiceDate\": \"2024-07-29T14:04:15.561Z\",\n", "        \"totalAmount\": 34.5,\n", "        \"amountPaid\": 0\n", "    },\n", "    {\n", "        \"fullName\": \"<PERSON> Hines23\",\n", "        \"invoiceDate\": \"2023-06-15T10:30:00.000Z\",\n", "        \"totalAmount\": 50.75,\n", "        \"amountPaid\": 20\n", "    },\n", "\n", "    {\n", "        \"fullName\": \"<PERSON> Hi<PERSON>22\",\n", "        \"invoiceDate\": \"2023-06-15T10:30:00.000Z\",\n", "        \"totalAmount\": 50.75,\n", "        \"amountPaid\": 20\n", "    }\n", "]\n", "headers = {\n", "    \"Authorization\": \"Bearer d394a9c6-e6fc-441b-aecb-602b6b53a0be\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "response = requests.request(\"POST\", url, json=payload, headers=headers)\n", "\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "# Define API endpoint and headers\n", "url = \"https://api.glideapps.com/tables/8c84d844-f065-4d78-beff-259527edba76/rows\"\n", "# url = \"https://api.glideapps.com/tables/\"\n", "headers = {\n", "    \"Authorization\": \"Bearer d394a9c6-e6fc-441b-aecb-602b6b53a0be\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "# Make the POST request\n", "response = requests.get(url, headers=headers)\n", "print(response.json())\n", "\n", "# Check the response\n", "if response.status_code == 200:\n", "    print(\"Data uploaded successfully!\")\n", "else:\n", "    print(f\"Failed to upload data. Status Code: {response.status_code}\")\n", "    print(f\"Response: {response.text}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "# Table ID and API token (replace with your actual values)\n", "# table_id = \"<your_table_id>\"\n", "# api_token = \"<your_api_token>\"\n", "\n", "# API endpoint\n", "url = f\"https://api.glideapps.com/tables/{table_id}/rows\"\n", "\n", "# Data to push\n", "data = {\n", "    \"ZPID\": \"121719385\",\n", "    \"url\": \"https://www.zillow.com/homedetails/121719385_zpid/\",\n", "    \"fetchDate\": \"2024-10-31 21:24:42\",\n", "    \"streetAddress\": \"64-73 Austin Street UNIT 5C\",\n", "    \"city\": \"Flushing\",\n", "    \"state\": \"NY\",\n", "    \"zipcode\": \"11374\",\n", "    \"price\": \"688000\",\n", "    \"bedrooms\": \"2\",\n", "    \"bathrooms\": \"2\",\n", "    \"livingArea\": \"856\",\n", "    \"yearBuilt\": \"2013\",\n", "    \"propertyType\": \"Condo\",\n", "    \"status\": \"FOR_SALE\",\n", "    \"daysOnZillow\": \"8\",\n", "    \"views\": \"366\",\n", "    \"saved\": \"14\",\n", "    \"priceHistory\": \"2024-10-25: $688,000 (Listed for sale)\",\n", "    \"description\": \"Stunning 2 Bed, 2 Bath Condo in the Heart of Rego Park!\",\n", "    \"schools\": \"Rating: 8 Name: Ps 139 Rego Park\",\n", "    \"hoaFee\": \"2476.8\",\n", "    \"appliances\": \"$532.00\",\n", "    \"heating\": \"Dishwasher, Dryer, Oven\",\n", "    \"cooling\": \"Electric, Baseboard\",\n", "    \"parkingFeatures\": \"Assigned, Garage\",\n", "    \"mlsId\": \"\",\n", "    \"mlsName\": \"3586837\",\n", "    \"agentName\": \"OneKey® MLS\",\n", "    \"agentPhoneNumber\": \"<PERSON>\",\n", "    \"brokerName\": \"************\",\n", "    \"image 1\": \"https://photos.zillowstatic.com/fp/5635c25a6d3ab4d50a6a7dc5e055f875-p_c.jpg\"\n", "}\n", "\n", "# Convert to a list if sending multiple rows\n", "payload = [data]\n", "\n", "# Headers\n", "headers = {\n", "    \"Authorization\": f\"Bearer {api_token}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "# Send POST request\n", "response = requests.post(url, json=payload, headers=headers)\n", "\n", "# Print response\n", "print(response.status_code)\n", "print(response.json())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define API endpoint and headers\n", "url = \"https://api.glideapp.io/tables/function/mutateTables\"\n", "headers = {\n", "    \"Authorization\": \"Bearer d394a9c6-e6fc-441b-aecb-602b6b53a0be\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "# Make the POST request\n", "response = requests.post(url, headers=headers, json=payload)\n", "\n", "# Check the response\n", "if response.status_code == 200:\n", "    print(\"Data uploaded successfully!\")\n", "else:\n", "    print(f\"Failed to upload data. Status Code: {response.status_code}\")\n", "    print(f\"Response: {response.text}\")\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'mutations' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[8], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m batch_size \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m100\u001b[39m  \u001b[38;5;66;03m# Adjust based on API limits\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m0\u001b[39m, \u001b[38;5;28mlen\u001b[39m(\u001b[43mmutations\u001b[49m), batch_size):\n\u001b[0;32m      3\u001b[0m     batch \u001b[38;5;241m=\u001b[39m mutations[i:i \u001b[38;5;241m+\u001b[39m batch_size]\n\u001b[0;32m      4\u001b[0m     payload[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmutations\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m batch\n", "\u001b[1;31mNameError\u001b[0m: name 'mutations' is not defined"]}], "source": ["batch_size = 100  # Adjust based on API limits\n", "for i in range(0, len(mutations), batch_size):\n", "    batch = mutations[i:i + batch_size]\n", "    payload[\"mutations\"] = batch\n", "    response = requests.post(url, headers=headers, json=payload)\n", "    \n", "    if response.status_code != 200:\n", "        print(f\"Failed at batch {i // batch_size}: {response.text}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_combined[\"image 1\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_combined.to_excel(\"11.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_df = df.reindex(columns=columns_to_keep)\n", "\n", "# Display the resulting DataFrame\n", "print(filtered_df)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 2}